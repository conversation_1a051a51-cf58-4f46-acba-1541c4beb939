{"username": "Test Player", "money": 4051, "level": {"current": 7, "exp": 13658, "required_to_next_level": 2500}, "race_level": 5, "cars": {"selected_car": 1, "car_colors": {"1": "1"}}, "inventory": {"owned_cars": ["test_car", "classic"], "owned_parts": {"engine": ["V10 Supercar"], "turbo": ["Variable Geometry Turbo"], "intercooler": [], "ecu": []}}, "usage_data": {"cars": {"0": {"car_age_days": 0, "races_completed": 0, "engine_age_days": 0, "turbo_age_days": 0, "intercooler_age_days": 0, "ecu_age_days": 0}}}, "fuel_data": {"cars": {"0": {"current_fuel": 55.8312, "max_capacity": 60, "last_refuel": 1750179878}}}, "tire_data": {"cars": {"0": {"tire_type": "standard", "condition": 45.55076319999999, "total_distance": 2250.0, "last_replacement": 1750179878}}}, "maintenance_data": {"cars": {"0": {"last_maintenance": 1750179878, "maintenance_due": false, "total_maintenance_cost": 0, "crashes": 0, "repair_history": []}}, "insurance": null}}