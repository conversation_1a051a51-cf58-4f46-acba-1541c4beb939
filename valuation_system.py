import json
import time
from datetime import datetime

class ValuationSystem:
    def __init__(self):
        # Base depreciation rates (per day)
        self.depreciation_rates = {
            "car": 0.002,      # 0.2% per day
            "engine": 0.001,   # 0.1% per day
            "turbo": 0.0015,   # 0.15% per day
            "intercooler": 0.0008,  # 0.08% per day
            "ecu": 0.0005      # 0.05% per day (software depreciates slowly)
        }
        
        # Usage depreciation (per race)
        self.usage_depreciation = {
            "car": 0.001,      # 0.1% per race
            "engine": 0.002,   # 0.2% per race
            "turbo": 0.0025,   # 0.25% per race
            "intercooler": 0.001,  # 0.1% per race
            "ecu": 0.0002      # 0.02% per race
        }
        
        # Performance multipliers (based on power-to-weight ratio)
        self.performance_multipliers = {
            "low": 0.8,        # < 0.3 P/W ratio
            "medium": 1.0,     # 0.3-0.8 P/W ratio
            "high": 1.2,       # 0.8-1.5 P/W ratio
            "extreme": 1.5     # > 1.5 P/W ratio
        }
        
        # Condition multipliers
        self.condition_multipliers = {
            "excellent": 1.0,   # 90-100% condition
            "good": 0.85,       # 70-89% condition
            "fair": 0.65,       # 50-69% condition
            "poor": 0.4,        # 30-49% condition
            "broken": 0.1       # < 30% condition
        }
    
    def calculate_car_value(self, car_data, usage_data=None):
        """Calculate current value of a car including all parts"""
        if usage_data is None:
            usage_data = self.get_default_usage_data()
        
        # Base car value
        base_car_value = car_data.get("value", 1000)
        
        # Calculate car depreciation
        car_condition = self.calculate_condition(
            usage_data.get("car_age_days", 0),
            usage_data.get("races_completed", 0),
            "car"
        )
        
        car_value = base_car_value * car_condition
        
        # Add parts value
        parts_value = 0
        parts = car_data.get("parts", {})
        
        for part_type, part_data in parts.items():
            if part_data is not None:
                part_value = self.calculate_part_value(
                    part_data, 
                    part_type, 
                    usage_data.get(f"{part_type}_age_days", 0),
                    usage_data.get("races_completed", 0)
                )
                parts_value += part_value
        
        # Calculate performance bonus
        performance_bonus = self.calculate_performance_bonus(car_data)
        
        total_value = (car_value + parts_value) * performance_bonus
        
        return {
            "total_value": int(total_value),
            "car_base_value": int(car_value),
            "parts_value": int(parts_value),
            "performance_bonus": performance_bonus,
            "car_condition": car_condition,
            "breakdown": self.get_value_breakdown(car_data, usage_data)
        }
    
    def calculate_part_value(self, part_data, part_type, age_days, races_completed):
        """Calculate current value of a single part"""
        base_value = part_data.get("value", 100)
        
        condition = self.calculate_condition(age_days, races_completed, part_type)
        
        return base_value * condition
    
    def calculate_condition(self, age_days, races_completed, item_type):
        """Calculate condition percentage based on age and usage"""
        # Time-based depreciation
        time_depreciation = age_days * self.depreciation_rates.get(item_type, 0.001)
        
        # Usage-based depreciation
        usage_depreciation = races_completed * self.usage_depreciation.get(item_type, 0.001)
        
        # Total depreciation (capped at 90% to prevent negative values)
        total_depreciation = min(0.9, time_depreciation + usage_depreciation)
        
        # Condition percentage
        condition = 1.0 - total_depreciation
        
        return max(0.1, condition)  # Minimum 10% value
    
    def calculate_performance_bonus(self, car_data):
        """Calculate performance bonus based on car's power-to-weight ratio"""
        # Calculate total horsepower
        engine = car_data.get("parts", {}).get("engine", {})
        base_hp = engine.get("horsepower", 100)
        
        # Calculate boost from other parts
        total_boost = 0
        parts = car_data.get("parts", {})
        
        for part_type in ["turbo", "intercooler", "ecu"]:
            part = parts.get(part_type)
            if part and "horsepower_boost_percentage" in part:
                total_boost += part["horsepower_boost_percentage"]
        
        total_hp = base_hp * (1 + total_boost / 100)
        
        # Calculate total weight
        base_weight = car_data.get("weight", 500)
        parts_weight = 0
        
        for part_type, part in parts.items():
            if part and "weight" in part:
                parts_weight += part["weight"]
        
        total_weight = base_weight + parts_weight
        
        # Calculate power-to-weight ratio
        power_to_weight = total_hp / total_weight if total_weight > 0 else 0
        
        # Determine performance category
        if power_to_weight < 0.3:
            return self.performance_multipliers["low"]
        elif power_to_weight < 0.8:
            return self.performance_multipliers["medium"]
        elif power_to_weight < 1.5:
            return self.performance_multipliers["high"]
        else:
            return self.performance_multipliers["extreme"]
    
    def get_condition_category(self, condition_percentage):
        """Get condition category from percentage"""
        if condition_percentage >= 0.9:
            return "excellent"
        elif condition_percentage >= 0.7:
            return "good"
        elif condition_percentage >= 0.5:
            return "fair"
        elif condition_percentage >= 0.3:
            return "poor"
        else:
            return "broken"
    
    def get_value_breakdown(self, car_data, usage_data):
        """Get detailed breakdown of value calculation"""
        breakdown = {
            "car": {
                "base_value": car_data.get("value", 1000),
                "condition": self.calculate_condition(
                    usage_data.get("car_age_days", 0),
                    usage_data.get("races_completed", 0),
                    "car"
                ),
                "current_value": 0
            },
            "parts": {}
        }
        
        # Calculate car current value
        breakdown["car"]["current_value"] = int(
            breakdown["car"]["base_value"] * breakdown["car"]["condition"]
        )
        
        # Calculate parts breakdown
        parts = car_data.get("parts", {})
        for part_type, part_data in parts.items():
            if part_data is not None:
                condition = self.calculate_condition(
                    usage_data.get(f"{part_type}_age_days", 0),
                    usage_data.get("races_completed", 0),
                    part_type
                )
                
                breakdown["parts"][part_type] = {
                    "base_value": part_data.get("value", 100),
                    "condition": condition,
                    "current_value": int(part_data.get("value", 100) * condition),
                    "condition_category": self.get_condition_category(condition)
                }
        
        return breakdown
    
    def get_default_usage_data(self):
        """Get default usage data for new items"""
        return {
            "car_age_days": 0,
            "races_completed": 0,
            "engine_age_days": 0,
            "turbo_age_days": 0,
            "intercooler_age_days": 0,
            "ecu_age_days": 0
        }
    
    def update_usage_data(self, usage_data, races_completed_increment=1):
        """Update usage data after races"""
        current_time = time.time()
        
        # Update races completed
        usage_data["races_completed"] = usage_data.get("races_completed", 0) + races_completed_increment
        
        # Update age if not set
        if "last_update" not in usage_data:
            usage_data["last_update"] = current_time
        
        # Calculate days passed since last update
        days_passed = (current_time - usage_data.get("last_update", current_time)) / (24 * 3600)
        
        # Update age for all components
        usage_data["car_age_days"] = usage_data.get("car_age_days", 0) + days_passed
        usage_data["engine_age_days"] = usage_data.get("engine_age_days", 0) + days_passed
        usage_data["turbo_age_days"] = usage_data.get("turbo_age_days", 0) + days_passed
        usage_data["intercooler_age_days"] = usage_data.get("intercooler_age_days", 0) + days_passed
        usage_data["ecu_age_days"] = usage_data.get("ecu_age_days", 0) + days_passed
        
        usage_data["last_update"] = current_time
        
        return usage_data
    
    def estimate_selling_price(self, car_data, usage_data=None):
        """Estimate selling price (typically 60-80% of current value)"""
        valuation = self.calculate_car_value(car_data, usage_data)
        
        # Selling price is typically 70% of current value
        selling_multiplier = 0.7
        
        # Adjust based on condition
        avg_condition = valuation["car_condition"]
        if avg_condition > 0.8:
            selling_multiplier = 0.75  # Better condition = better selling price
        elif avg_condition < 0.5:
            selling_multiplier = 0.6   # Poor condition = lower selling price
        
        selling_price = int(valuation["total_value"] * selling_multiplier)
        
        return {
            "selling_price": selling_price,
            "current_value": valuation["total_value"],
            "selling_percentage": int(selling_multiplier * 100)
        }

# Global valuation system instance
valuation_system = ValuationSystem()
