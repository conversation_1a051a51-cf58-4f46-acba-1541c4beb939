import pygame
import time

class BaseCar:
    def __init__(self, name, color, weight, parts, x_cord, y_cord, start_time):
        self.name = name
        self.img = pygame.image.load(f'./assets/img/{name}_{color}_car.png')
        
        # Calculate total weight including all parts
        parts_weight = 0
        for part_key, part_value in parts.items():
            if part_value is not None and "weight" in part_value:
                parts_weight += part_value["weight"]
        self.total_weight = weight + parts_weight
        
        # Base horsepower from engine
        base_hp = parts["engine"]["horsepower"] if parts["engine"] else 0
        
        # Calculate total horsepower boost percentage from other parts
        boost_percentage = 0
        for part_key, part_value in parts.items():
            if part_key != "engine" and part_value is not None and "horsepower_boost_percentage" in part_value:
                boost_percentage += part_value["horsepower_boost_percentage"]
        
        # Calculate final horsepower with boosts
        self.horsepower = int(base_hp * (1 + boost_percentage / 100))
        
        ratio = self.horsepower / self.total_weight if self.total_weight > 0 else 0
        self.max_speed = 300 + ratio * 2000
        self.acceleration = 100 + ratio * 1000
        self.speed = 0
        self._pos = pygame.math.Vector2(x_cord, y_cord)
        self.x_cord = int(x_cord)
        self.y_cord = y_cord
        self.start_time = start_time

    def elapsed_time(self, map_distance):
        if self._pos.x >= map_distance:
            return time.time() - self.start_time

class Car(BaseCar):
    def update(self, keys, dt):
        if keys is None or dt is None:
            return
        if keys[pygame.K_w]:
            self.speed = min(self.speed + self.acceleration * dt, self.max_speed)
        else:
            self.speed = max(self.speed - self.acceleration * dt * 0.5, 0)
        self._pos.x += self.speed * dt
        self.x_cord = int(self._pos.x)

    def draw(self, screen):
        font = pygame.font.SysFont("arial", 36)
        text = font.render(f"Twoja prędkość {int(self.speed // 5)} km/h", True, (255, 255, 255))
        screen.blit(text, (30, 30))
        screen.blit(self.img, (self.x_cord, self.y_cord))

    def set_static_position(self, pos_x):
        self._pos.x = pos_x
        self.x_cord = pos_x

class OpponentCar(BaseCar):
    def __init__(self, name, color, weight, parts, x_cord, y_cord, start_time, opponent_name="Przeciwnik"):
        super().__init__(name, color, weight, parts, x_cord, y_cord, start_time)
        self.opponent_name = opponent_name
        self._target_speed = self.max_speed * 0.95
        self.frame_count = 0

    def update(self, dt):
        self.frame_count += 1
        if self.frame_count % 60 == 0:
            self._target_speed = self.max_speed * 0.9 + self.max_speed * 0.1 * pygame.time.get_ticks() % 0.1
        speed_diff = self._target_speed - self.speed
        self.speed += speed_diff * 0.05
        self.speed = min(self.speed, self.max_speed)
        self._pos.x += self.speed * dt
        self.x_cord = int(self._pos.x)

    def draw(self, screen, distance_covered, is_map_ended, bg_x_cord):
        x = self.x_cord - distance_covered if not is_map_ended else self.x_cord
        screen.blit(self.img, (x, self.y_cord))

        # Draw opponent info
        font = pygame.font.SysFont("arial", 24)
        speed_text = font.render(f"{self.opponent_name}: {int(self.speed // 5)} km/h", True, (255, 255, 255))
        screen.blit(speed_text, (30, 80))